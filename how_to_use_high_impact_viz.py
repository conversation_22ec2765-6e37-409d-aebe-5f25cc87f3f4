#!/usr/bin/env python3
"""
如何调用高影响力可视化的示例
"""

# 方式1：直接运行 explain.py
# python explain.py
# 这会自动生成两个文件：
# - explanation_high_impact_node_118508.png (高影响力)
# - explanation_medium_impact_node_118508.png (中等影响力)

# 方式2：在你的代码中导入并使用
def example_usage():
    """在你的代码中使用高影响力可视化"""
    
    # 导入函数
    from explain import visualize_high_impact_explanation, quick_high_impact_viz
    
    # 假设你已经有了这些变量：
    # graph, edge_mask, node_feat_mask, node_idx
    
    # === 方法1：使用便捷函数 ===
    quick_high_impact_viz(
        graph=graph,
        edge_mask=edge_mask,
        node_mask=node_feat_mask,
        node_idx=node_idx,
        save_path='my_high_impact.png'
    )
    
    # === 方法2：使用完整函数 ===
    # 高影响力：只显示最重要的5%边和10%节点
    visualize_high_impact_explanation(
        graph=graph,
        edge_mask=edge_mask,
        node_mask=node_feat_mask,
        node_idx=node_idx,
        impact_level='high',
        save_path='high_impact.png'
    )
    
    # 中等影响力：显示15%边和25%节点
    visualize_high_impact_explanation(
        graph=graph,
        edge_mask=edge_mask,
        node_mask=node_feat_mask,
        node_idx=node_idx,
        impact_level='medium',
        save_path='medium_impact.png'
    )
    
    # 低影响力：显示30%边和40%节点
    visualize_high_impact_explanation(
        graph=graph,
        edge_mask=edge_mask,
        node_mask=node_feat_mask,
        node_idx=node_idx,
        impact_level='low',
        save_path='low_impact.png'
    )

# 方式3：在解释流程中直接使用
def explanation_with_high_impact_viz():
    """完整的解释+可视化流程"""
    
    from explain import GATExplainerWrapper, GNNExplainer, quick_high_impact_viz
    from utils.loaddata import load_entity_level_dataset
    from model.autoencoder import build_model
    import torch
    
    # 加载模型和数据 (这里是示例代码)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    # model = build_model(args).to(device)
    # graph = load_entity_level_dataset('theia', 'test', 0).to(device)
    
    # 创建解释器
    # wrapped_model = GATExplainerWrapper(model)
    # explainer = GNNExplainer(model=wrapped_model, num_hops=3)
    
    # 生成解释
    # node_idx = 118508
    # new_center, subgraph, node_feat_mask, edge_mask = explainer.explain_node(node_idx, graph, graph.ndata['attr'])
    
    # 直接调用高影响力可视化
    # quick_high_impact_viz(
    #     graph=subgraph,
    #     edge_mask=edge_mask,
    #     node_mask=node_feat_mask,
    #     node_idx=new_center,
    #     save_path=f'high_impact_explanation_{node_idx}.png'
    # )
    
    print("示例代码 - 取消注释后可运行")

if __name__ == "__main__":
    print("=== 高影响力可视化调用方法 ===")
    print()
    print("1. 直接运行 explain.py：")
    print("   python explain.py")
    print("   会自动生成高影响力和中等影响力两个可视化图片")
    print()
    print("2. 在代码中导入使用：")
    print("   from explain import quick_high_impact_viz")
    print("   quick_high_impact_viz(graph, edge_mask, node_mask, node_idx)")
    print()
    print("3. 使用不同影响级别：")
    print("   from explain import visualize_high_impact_explanation")
    print("   visualize_high_impact_explanation(graph, edge_mask, node_mask, node_idx, impact_level='high')")
    print()
    print("影响级别说明：")
    print("- 'high': 只显示前5%边和前10%节点 (最清晰)")
    print("- 'medium': 显示前15%边和前25%节点 (平衡)")
    print("- 'low': 显示前30%边和前40%节点 (详细)")
