from attack import choose_poisoning_node, run_backdoor_attack
import torch
import numpy as np
import random
import os
import time

class Config:
    """配置类,包含所有实验参数
    """
    def __init__(self):
        # 数据集配置
        self.dataset = "theia"
        self.poison_ratio = 0.03

        # 训练配置
        self.epochs = 60
        self.batch_size = 5000
        self.lr_d = 0.001  # 检测器学习率
        self.lr_g = 0.0003  # glubel 0.0003   GW 0.001
        self.weight_decay = 5e-4  # 检测器 weight_decay   5e-4  5e-3
        self.optimizer = 'adam'

        self.epochs_d = 1   # 检测器训练轮数
        self.epochs_g = 2   # 生成器训练轮数

        # 触发器配置
        self.trigger_shape = (2,1)  # (进程数,文件数)
        # self.trigger_shape = (4, 15)  # (进程数,文件数)
        # self.thre = 0.5  # 触发器阈值
        self.k_hop = 2  # 触发器生成器输入k阶入子图

        # GPU配置
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.seed = 0
        # 学习率调度器参数
        self.lr_scheduler_step_size = 33  # 每隔多少epoch衰减一次
        self.lr_scheduler_gamma = 0.5     # 衰减系数

        # 批处理优化配置
        self.trigger_batch_size = 32  # 触发器生成批处理大小
        self.embedding_batch_size = 1024  # 嵌入计算批处理大小

def set_random_seed(seed):
    """设置随机种子以确保可重复性
    
    参数:
        seed: 随机种子
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)  # if you are using multi-GPU.
    os.environ['PYTHONHASHSEED'] = str(seed)
    
    # These are crucial for reproducibility
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def main():
    # 初始化配置
    cfg = Config()
    set_random_seed(cfg.seed)
    
    # 1. 投毒节点选择
    
    # 2. 执行后门攻击
    run_backdoor_attack(cfg)
    
    # 3. 测试攻击效果
    # test_backdoor_attack(cfg, poison=True)

if __name__ == "__main__":
    main()