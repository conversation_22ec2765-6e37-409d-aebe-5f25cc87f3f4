#!/usr/bin/env python3
"""
自动测试多个触发器大小性能的脚本

该脚本会：
1. 自动修改 poison_main.py 中的 trigger_shape 参数
2. 依次执行 train.py 和 eval.py
3. 记录每个配置的性能结果
4. 生成性能对比报告
"""

import os
import sys
import subprocess
import time
import json
import re
from datetime import datetime
from pathlib import Path

class TriggerShapeAutoTester:
    def __init__(self, config_file="auto_test_config.json"):
        """
        初始化自动测试器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.poison_main_file = "poison_main.py"
        self.train_script = "train.py"
        self.eval_script = "eval.py"
        self.results_dir = "auto_test_results"
        self.current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建结果目录
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 默认测试配置
        self.default_trigger_shapes = [
            (1, 1),   # 小触发器
            (2, 1),   # 当前配置
            (3, 1),   # 中等触发器
            (4, 1),   # 较大触发器
            (2, 2),   # 增加文件数
            (3, 2),   # 中等配置
            (4, 2),   # 较大配置
            (3, 3),   # 平衡配置
            (4, 4),   # 大触发器
            (5, 5),   # 很大触发器
        ]
        
        self.load_config()
        
    def load_config(self):
        """加载测试配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.trigger_shapes = config.get('trigger_shapes', self.default_trigger_shapes)
                self.skip_train = config.get('skip_train', False)
                self.skip_eval = config.get('skip_eval', False)
                self.timeout = config.get('timeout', 3600)  # 默认1小时超时
                print(f"已加载配置文件: {self.config_file}")
            except Exception as e:
                print(f"加载配置文件失败: {e}，使用默认配置")
                self.trigger_shapes = self.default_trigger_shapes
                self.skip_train = False
                self.skip_eval = False
                self.timeout = 3600
        else:
            # 创建默认配置文件
            self.trigger_shapes = self.default_trigger_shapes
            self.skip_train = False
            self.skip_eval = False
            self.timeout = 3600
            self.save_config()
            
    def save_config(self):
        """保存配置文件"""
        config = {
            'trigger_shapes': self.trigger_shapes,
            'skip_train': self.skip_train,
            'skip_eval': self.skip_eval,
            'timeout': self.timeout,
            'description': '自动测试配置文件',
            'trigger_shapes_description': '要测试的触发器形状列表，格式为 [进程数, 文件数]',
            'skip_train_description': '是否跳过训练步骤（如果模型已训练）',
            'skip_eval_description': '是否跳过评估步骤',
            'timeout_description': '每个步骤的超时时间（秒）'
        }
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print(f"配置文件已保存: {self.config_file}")
    
    def backup_original_file(self):
        """备份原始的 poison_main.py 文件"""
        backup_file = f"{self.poison_main_file}.backup_{self.current_time}"
        if not os.path.exists(backup_file):
            subprocess.run(['copy', self.poison_main_file, backup_file], shell=True)
            print(f"已备份原始文件: {backup_file}")
        return backup_file
    
    def modify_trigger_shape(self, trigger_shape):
        """
        修改 poison_main.py 中的 trigger_shape 参数
        
        Args:
            trigger_shape: 新的触发器形状，格式为 (进程数, 文件数)
        """
        try:
            with open(self.poison_main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 使用正则表达式替换 trigger_shape 行
            pattern = r'(\s*self\.trigger_shape\s*=\s*)\([^)]+\)(\s*#.*)?'
            replacement = f'\\1{trigger_shape}\\2'
            
            new_content = re.sub(pattern, replacement, content)
            
            if new_content == content:
                print(f"警告: 未找到 trigger_shape 参数行，尝试其他模式...")
                # 尝试更宽松的模式
                pattern = r'(trigger_shape\s*=\s*)\([^)]+\)'
                replacement = f'\\1{trigger_shape}'
                new_content = re.sub(pattern, replacement, content)
            
            with open(self.poison_main_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"已修改 trigger_shape 为: {trigger_shape}")
            return True
            
        except Exception as e:
            print(f"修改 trigger_shape 失败: {e}")
            return False
    
    def run_command(self, command, log_file, timeout=None):
        """
        执行命令并记录输出
        
        Args:
            command: 要执行的命令
            log_file: 日志文件路径
            timeout: 超时时间（秒）
        
        Returns:
            (success, execution_time, return_code)
        """
        print(f"执行命令: {command}")
        print(f"日志文件: {log_file}")
        
        start_time = time.time()
        
        try:
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"命令: {command}\n")
                f.write(f"开始时间: {datetime.now()}\n")
                f.write("=" * 50 + "\n")
                f.flush()
                
                process = subprocess.Popen(
                    command,
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    universal_newlines=True,
                    bufsize=1
                )
                
                # 实时写入输出
                for line in process.stdout:
                    f.write(line)
                    f.flush()
                    print(line.rstrip())  # 同时显示在控制台
                
                # 等待进程完成
                return_code = process.wait(timeout=timeout or self.timeout)
                
        except subprocess.TimeoutExpired:
            process.kill()
            execution_time = time.time() - start_time
            print(f"命令执行超时 ({timeout or self.timeout}秒)")
            return False, execution_time, -1
            
        except Exception as e:
            execution_time = time.time() - start_time
            print(f"命令执行失败: {e}")
            return False, execution_time, -1
        
        execution_time = time.time() - start_time
        success = return_code == 0
        
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write("=" * 50 + "\n")
            f.write(f"结束时间: {datetime.now()}\n")
            f.write(f"执行时间: {execution_time:.2f}秒\n")
            f.write(f"返回码: {return_code}\n")
        
        print(f"命令执行{'成功' if success else '失败'}, 耗时: {execution_time:.2f}秒")
        return success, execution_time, return_code
    
    def extract_performance_metrics(self, log_file):
        """
        从日志文件中提取性能指标
        
        Args:
            log_file: 日志文件路径
            
        Returns:
            dict: 性能指标字典
        """
        metrics = {}
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取训练损失
            train_loss_pattern = r'train_loss:\s*([\d.]+)'
            train_losses = re.findall(train_loss_pattern, content)
            if train_losses:
                metrics['final_train_loss'] = float(train_losses[-1])
                metrics['avg_train_loss'] = sum(float(x) for x in train_losses) / len(train_losses)
            
            # 提取评估指标（根据实际输出格式调整）
            # 这里需要根据你的eval.py输出格式来调整正则表达式
            auc_pattern = r'AUC[:\s]*([\d.]+)'
            auc_matches = re.findall(auc_pattern, content)
            if auc_matches:
                metrics['auc'] = float(auc_matches[-1])
            
            # 提取准确率
            acc_pattern = r'[Aa]ccuracy[:\s]*([\d.]+)'
            acc_matches = re.findall(acc_pattern, content)
            if acc_matches:
                metrics['accuracy'] = float(acc_matches[-1])
            
            # 提取F1分数
            f1_pattern = r'F1[:\s]*([\d.]+)'
            f1_matches = re.findall(f1_pattern, content)
            if f1_matches:
                metrics['f1'] = float(f1_matches[-1])
                
        except Exception as e:
            print(f"提取性能指标失败: {e}")
        
        return metrics
    
    def test_single_trigger_shape(self, trigger_shape):
        """
        测试单个触发器形状
        
        Args:
            trigger_shape: 触发器形状 (进程数, 文件数)
            
        Returns:
            dict: 测试结果
        """
        print(f"\n{'='*60}")
        print(f"开始测试触发器形状: {trigger_shape}")
        print(f"{'='*60}")
        
        # 创建该配置的结果目录
        shape_str = f"{trigger_shape[0]}x{trigger_shape[1]}"
        result_dir = os.path.join(self.results_dir, f"trigger_{shape_str}_{self.current_time}")
        os.makedirs(result_dir, exist_ok=True)
        
        result = {
            'trigger_shape': trigger_shape,
            'shape_str': shape_str,
            'start_time': datetime.now().isoformat(),
            'train_success': False,
            'eval_success': False,
            'train_time': 0,
            'eval_time': 0,
            'metrics': {}
        }
        
        # 修改触发器形状
        if not self.modify_trigger_shape(trigger_shape):
            result['error'] = '修改触发器形状失败'
            return result
        
        # 执行训练
        if not self.skip_train:
            print(f"\n开始训练阶段...")
            train_log = os.path.join(result_dir, 'train.log')
            train_success, train_time, train_code = self.run_command(
                f'python {self.train_script}',
                train_log
            )
            result['train_success'] = train_success
            result['train_time'] = train_time
            result['train_return_code'] = train_code
            
            if not train_success:
                result['error'] = f'训练失败，返回码: {train_code}'
                return result
        else:
            print("跳过训练阶段")
        
        # 执行评估
        if not self.skip_eval:
            print(f"\n开始评估阶段...")
            eval_log = os.path.join(result_dir, 'eval.log')
            eval_success, eval_time, eval_code = self.run_command(
                f'python {self.eval_script}',
                eval_log
            )
            result['eval_success'] = eval_success
            result['eval_time'] = eval_time
            result['eval_return_code'] = eval_code
            
            if eval_success:
                # 提取性能指标
                result['metrics'] = self.extract_performance_metrics(eval_log)
            else:
                result['error'] = f'评估失败，返回码: {eval_code}'
        else:
            print("跳过评估阶段")
        
        result['end_time'] = datetime.now().isoformat()
        result['total_time'] = result['train_time'] + result['eval_time']
        
        # 保存单个结果
        result_file = os.path.join(result_dir, 'result.json')
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"测试完成: {trigger_shape}")
        if result.get('metrics'):
            print(f"性能指标: {result['metrics']}")
        
        return result
    
    def run_all_tests(self):
        """运行所有测试"""
        print(f"开始自动测试，共 {len(self.trigger_shapes)} 个配置")
        print(f"测试配置: {self.trigger_shapes}")
        
        # 备份原始文件
        backup_file = self.backup_original_file()
        
        all_results = []
        
        try:
            for i, trigger_shape in enumerate(self.trigger_shapes, 1):
                print(f"\n进度: {i}/{len(self.trigger_shapes)}")
                result = self.test_single_trigger_shape(trigger_shape)
                all_results.append(result)
                
                # 保存中间结果
                self.save_summary_report(all_results, f"intermediate_results_{self.current_time}.json")
                
        except KeyboardInterrupt:
            print("\n用户中断测试")
        except Exception as e:
            print(f"\n测试过程中发生错误: {e}")
        finally:
            # 恢复原始文件
            if os.path.exists(backup_file):
                subprocess.run(['copy', backup_file, self.poison_main_file], shell=True)
                print(f"已恢复原始文件")
        
        # 生成最终报告
        self.save_summary_report(all_results, f"final_results_{self.current_time}.json")
        self.generate_performance_report(all_results)
        
        return all_results
    
    def save_summary_report(self, results, filename):
        """保存汇总报告"""
        report_file = os.path.join(self.results_dir, filename)
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"汇总报告已保存: {report_file}")
    
    def generate_performance_report(self, results):
        """生成性能对比报告"""
        report_file = os.path.join(self.results_dir, f"performance_report_{self.current_time}.txt")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("触发器形状性能测试报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"测试时间: {self.current_time}\n")
            f.write(f"测试配置数量: {len(results)}\n\n")
            
            # 成功的测试
            successful_tests = [r for r in results if r.get('eval_success', False)]
            f.write(f"成功完成的测试: {len(successful_tests)}/{len(results)}\n\n")
            
            if successful_tests:
                f.write("性能对比:\n")
                f.write("-" * 80 + "\n")
                f.write(f"{'触发器形状':<12} {'训练时间':<10} {'评估时间':<10} {'总时间':<10} {'AUC':<8} {'准确率':<8} {'F1':<8}\n")
                f.write("-" * 80 + "\n")
                
                for result in successful_tests:
                    shape = result['shape_str']
                    train_time = f"{result['train_time']:.1f}s"
                    eval_time = f"{result['eval_time']:.1f}s"
                    total_time = f"{result['total_time']:.1f}s"
                    
                    metrics = result.get('metrics', {})
                    auc = f"{metrics.get('auc', 0):.3f}" if 'auc' in metrics else "N/A"
                    acc = f"{metrics.get('accuracy', 0):.3f}" if 'accuracy' in metrics else "N/A"
                    f1 = f"{metrics.get('f1', 0):.3f}" if 'f1' in metrics else "N/A"
                    
                    f.write(f"{shape:<12} {train_time:<10} {eval_time:<10} {total_time:<10} {auc:<8} {acc:<8} {f1:<8}\n")
                
                # 找出最佳配置
                if any('auc' in r.get('metrics', {}) for r in successful_tests):
                    best_auc = max(successful_tests, key=lambda x: x.get('metrics', {}).get('auc', 0))
                    f.write(f"\n最佳AUC配置: {best_auc['shape_str']} (AUC: {best_auc['metrics'].get('auc', 0):.3f})\n")
                
                if any('accuracy' in r.get('metrics', {}) for r in successful_tests):
                    best_acc = max(successful_tests, key=lambda x: x.get('metrics', {}).get('accuracy', 0))
                    f.write(f"最佳准确率配置: {best_acc['shape_str']} (准确率: {best_acc['metrics'].get('accuracy', 0):.3f})\n")
            
            # 失败的测试
            failed_tests = [r for r in results if not r.get('eval_success', False)]
            if failed_tests:
                f.write(f"\n失败的测试:\n")
                for result in failed_tests:
                    f.write(f"- {result['shape_str']}: {result.get('error', '未知错误')}\n")
        
        print(f"性能报告已生成: {report_file}")


def main():
    """主函数"""
    print("触发器形状自动测试工具")
    print("=" * 50)
    
    # 检查必要文件
    required_files = ['poison_main.py', 'train.py', 'eval.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"错误: 缺少必要文件: {missing_files}")
        return
    
    # 创建测试器
    tester = TriggerShapeAutoTester()
    
    # 显示配置
    print(f"将测试以下触发器形状: {tester.trigger_shapes}")
    print(f"跳过训练: {tester.skip_train}")
    print(f"跳过评估: {tester.skip_eval}")
    print(f"超时设置: {tester.timeout}秒")
    
    # 确认开始
    response = input("\n是否开始测试? (y/N): ").strip().lower()
    if response != 'y':
        print("测试已取消")
        return
    
    # 运行测试
    results = tester.run_all_tests()
    
    print(f"\n测试完成! 结果保存在: {tester.results_dir}")
    print(f"成功完成: {len([r for r in results if r.get('eval_success', False)])}/{len(results)}")


if __name__ == "__main__":
    main()
