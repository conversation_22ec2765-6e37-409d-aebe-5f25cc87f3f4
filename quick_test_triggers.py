#!/usr/bin/env python3
"""
快速触发器测试脚本

简化版本，用于快速测试几个触发器大小
"""

import os
import subprocess
import time
import re
from datetime import datetime

def modify_trigger_shape(trigger_shape):
    """修改 poison_main.py 中的 trigger_shape 参数"""
    poison_main_file = "poison_main.py"
    
    try:
        with open(poison_main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用正则表达式替换 trigger_shape 行
        pattern = r'(\s*self\.trigger_shape\s*=\s*)\([^)]+\)(\s*#.*)?'
        replacement = f'\\1{trigger_shape}\\2'
        
        new_content = re.sub(pattern, replacement, content)
        
        if new_content == content:
            print(f"警告: 未找到 trigger_shape 参数行")
            return False
        
        with open(poison_main_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✓ 已修改 trigger_shape 为: {trigger_shape}")
        return True
        
    except Exception as e:
        print(f"✗ 修改 trigger_shape 失败: {e}")
        return False

def run_script(script_name, log_file=None):
    """运行脚本并返回是否成功"""
    print(f"  执行 {script_name}...")
    start_time = time.time()
    
    try:
        if log_file:
            with open(log_file, 'w', encoding='utf-8') as f:
                result = subprocess.run(
                    f'python {script_name}',
                    shell=True,
                    stdout=f,
                    stderr=subprocess.STDOUT,
                    timeout=1800  # 30分钟超时
                )
        else:
            result = subprocess.run(
                f'python {script_name}',
                shell=True,
                timeout=1800
            )
        
        execution_time = time.time() - start_time
        success = result.returncode == 0
        
        if success:
            print(f"  ✓ {script_name} 完成 ({execution_time:.1f}s)")
        else:
            print(f"  ✗ {script_name} 失败 (返回码: {result.returncode})")
        
        return success, execution_time
        
    except subprocess.TimeoutExpired:
        print(f"  ✗ {script_name} 超时")
        return False, time.time() - start_time
    except Exception as e:
        print(f"  ✗ {script_name} 错误: {e}")
        return False, time.time() - start_time

def backup_file():
    """备份原始文件"""
    poison_main_file = "poison_main.py"
    backup_file = f"{poison_main_file}.backup"
    
    if not os.path.exists(backup_file):
        subprocess.run(['copy', poison_main_file, backup_file], shell=True)
        print(f"✓ 已备份原始文件: {backup_file}")
    return backup_file

def restore_file(backup_file):
    """恢复原始文件"""
    poison_main_file = "poison_main.py"
    if os.path.exists(backup_file):
        subprocess.run(['copy', backup_file, poison_main_file], shell=True)
        print(f"✓ 已恢复原始文件")

def test_trigger_shapes(trigger_shapes, run_train=True, run_eval=True, save_logs=False):
    """
    测试多个触发器形状
    
    Args:
        trigger_shapes: 触发器形状列表，例如 [(2,1), (3,1), (4,1)]
        run_train: 是否运行训练
        run_eval: 是否运行评估
        save_logs: 是否保存详细日志
    """
    print("=" * 60)
    print("快速触发器测试")
    print("=" * 60)
    print(f"测试配置: {trigger_shapes}")
    print(f"运行训练: {run_train}")
    print(f"运行评估: {run_eval}")
    print(f"保存日志: {save_logs}")
    print()
    
    # 备份原始文件
    backup_file = backup_file()
    
    results = []
    
    try:
        for i, trigger_shape in enumerate(trigger_shapes, 1):
            print(f"[{i}/{len(trigger_shapes)}] 测试触发器形状: {trigger_shape}")
            
            # 修改配置
            if not modify_trigger_shape(trigger_shape):
                results.append({
                    'trigger_shape': trigger_shape,
                    'success': False,
                    'error': '修改配置失败'
                })
                continue
            
            result = {
                'trigger_shape': trigger_shape,
                'success': True,
                'train_success': True,
                'eval_success': True,
                'train_time': 0,
                'eval_time': 0
            }
            
            # 运行训练
            if run_train:
                log_file = f"train_{trigger_shape[0]}x{trigger_shape[1]}.log" if save_logs else None
                train_success, train_time = run_script('train.py', log_file)
                result['train_success'] = train_success
                result['train_time'] = train_time
                
                if not train_success:
                    result['success'] = False
                    result['error'] = '训练失败'
                    results.append(result)
                    continue
            
            # 运行评估
            if run_eval:
                log_file = f"eval_{trigger_shape[0]}x{trigger_shape[1]}.log" if save_logs else None
                eval_success, eval_time = run_script('eval.py', log_file)
                result['eval_success'] = eval_success
                result['eval_time'] = eval_time
                
                if not eval_success:
                    result['success'] = False
                    result['error'] = '评估失败'
            
            result['total_time'] = result['train_time'] + result['eval_time']
            results.append(result)
            
            print(f"  总耗时: {result['total_time']:.1f}s")
            print()
    
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
    finally:
        # 恢复原始文件
        restore_file(backup_file)
    
    # 显示结果摘要
    print("=" * 60)
    print("测试结果摘要")
    print("=" * 60)
    
    successful_tests = [r for r in results if r['success']]
    print(f"成功完成: {len(successful_tests)}/{len(results)}")
    
    if successful_tests:
        print("\n性能对比:")
        print(f"{'触发器形状':<12} {'训练时间':<10} {'评估时间':<10} {'总时间':<10}")
        print("-" * 50)
        
        for result in successful_tests:
            shape = f"{result['trigger_shape'][0]}x{result['trigger_shape'][1]}"
            train_time = f"{result['train_time']:.1f}s" if run_train else "跳过"
            eval_time = f"{result['eval_time']:.1f}s" if run_eval else "跳过"
            total_time = f"{result['total_time']:.1f}s"
            
            print(f"{shape:<12} {train_time:<10} {eval_time:<10} {total_time:<10}")
    
    # 显示失败的测试
    failed_tests = [r for r in results if not r['success']]
    if failed_tests:
        print(f"\n失败的测试:")
        for result in failed_tests:
            shape = f"{result['trigger_shape'][0]}x{result['trigger_shape'][1]}"
            error = result.get('error', '未知错误')
            print(f"- {shape}: {error}")
    
    return results

def main():
    """主函数"""
    print("快速触发器测试工具")
    
    # 检查必要文件
    required_files = ['poison_main.py', 'train.py', 'eval.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"错误: 缺少必要文件: {missing_files}")
        return
    
    # 默认测试配置
    trigger_shapes = [
        (2, 1),   # 当前配置
        (3, 1),   # 稍大一点
        (4, 1),   # 更大
        (2, 2),   # 增加文件数
        (3, 2),   # 平衡配置
    ]
    
    print(f"\n默认测试配置: {trigger_shapes}")
    
    # 用户选择
    print("\n选择测试模式:")
    print("1. 快速测试 (只运行评估)")
    print("2. 完整测试 (训练+评估)")
    print("3. 自定义配置")
    print("4. 退出")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == '1':
        # 快速测试 - 假设模型已训练
        test_trigger_shapes(trigger_shapes, run_train=False, run_eval=True)
    
    elif choice == '2':
        # 完整测试
        test_trigger_shapes(trigger_shapes, run_train=True, run_eval=True, save_logs=True)
    
    elif choice == '3':
        # 自定义配置
        print("\n输入触发器形状 (格式: 进程数,文件数)，每行一个，空行结束:")
        custom_shapes = []
        while True:
            line = input().strip()
            if not line:
                break
            try:
                parts = line.split(',')
                if len(parts) == 2:
                    shape = (int(parts[0].strip()), int(parts[1].strip()))
                    custom_shapes.append(shape)
                    print(f"  添加: {shape}")
                else:
                    print("  格式错误，请使用 '进程数,文件数' 格式")
            except ValueError:
                print("  格式错误，请输入数字")
        
        if custom_shapes:
            run_train = input("\n是否运行训练? (y/N): ").strip().lower() == 'y'
            run_eval = input("是否运行评估? (Y/n): ").strip().lower() != 'n'
            save_logs = input("是否保存详细日志? (y/N): ").strip().lower() == 'y'
            
            test_trigger_shapes(custom_shapes, run_train, run_eval, save_logs)
        else:
            print("没有有效的配置")
    
    elif choice == '4':
        print("退出")
        return
    
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
