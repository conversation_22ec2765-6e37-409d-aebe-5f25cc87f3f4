# 触发器形状自动测试工具

这个工具可以自动测试多个触发器大小的性能，帮助你找到最佳的触发器配置。

## 文件说明

1. **auto_test_trigger_shapes.py** - 完整的自动测试脚本
2. **quick_test_triggers.py** - 简化的快速测试脚本
3. **auto_test_config.json** - 配置文件
4. **README_auto_test.md** - 使用说明（本文件）

## 快速开始

### 方法1: 使用快速测试脚本（推荐）

```bash
python quick_test_triggers.py
```

这个脚本提供交互式界面，你可以选择：
- 快速测试（只运行评估，假设模型已训练）
- 完整测试（训练+评估）
- 自定义配置

### 方法2: 使用完整测试脚本

```bash
python auto_test_trigger_shapes.py
```

这个脚本会读取配置文件并运行完整的测试流程。

## 配置说明

### auto_test_config.json 配置文件

```json
{
  "trigger_shapes": [
    [1, 1],    // 触发器形状：[进程数, 文件数]
    [2, 1],
    [3, 1],
    [4, 1],
    [2, 2],
    [3, 2],
    [4, 2],
    [3, 3],
    [4, 4],
    [5, 5]
  ],
  "skip_train": false,     // 是否跳过训练步骤
  "skip_eval": false,      // 是否跳过评估步骤
  "timeout": 3600          // 超时时间（秒）
}
```

### 参数说明

- **trigger_shapes**: 要测试的触发器形状列表
  - 格式：[进程数, 文件数]
  - 例如：[2, 1] 表示 2个进程，1个文件
  
- **skip_train**: 如果你已经有训练好的模型，可以设置为 `true` 跳过训练
- **skip_eval**: 如果只想训练不想评估，可以设置为 `true`
- **timeout**: 每个步骤的超时时间，防止某个配置卡住

## 工作流程

1. **备份原始文件**: 自动备份 `poison_main.py`
2. **修改配置**: 自动修改 `self.trigger_shape` 参数
3. **执行训练**: 运行 `python train.py`
4. **执行评估**: 运行 `python eval.py`
5. **记录结果**: 保存日志和性能指标
6. **恢复原始文件**: 测试完成后恢复原始配置

## 输出结果

### 结果目录结构

```
auto_test_results/
├── trigger_2x1_20240712_143022/
│   ├── train.log
│   ├── eval.log
│   └── result.json
├── trigger_3x1_20240712_143022/
│   ├── train.log
│   ├── eval.log
│   └── result.json
├── final_results_20240712_143022.json
└── performance_report_20240712_143022.txt
```

### 性能报告示例

```
触发器形状性能测试报告
==================================================
测试时间: 20240712_143022
测试配置数量: 5

成功完成的测试: 4/5

性能对比:
--------------------------------------------------------------------------------
触发器形状    训练时间    评估时间    总时间     AUC      准确率    F1      
--------------------------------------------------------------------------------
2x1          120.5s     45.2s      165.7s    0.892    0.856    0.834   
3x1          145.3s     52.1s      197.4s    0.905    0.871    0.849   
4x1          178.9s     61.3s      240.2s    0.918    0.883    0.862   
2x2          156.7s     48.9s      205.6s    0.901    0.864    0.841   

最佳AUC配置: 4x1 (AUC: 0.918)
最佳准确率配置: 4x1 (准确率: 0.883)

失败的测试:
- 5x5: 训练超时
```

## 使用建议

### 1. 首次使用

建议先用快速测试脚本测试几个小的配置：

```bash
python quick_test_triggers.py
# 选择 "1. 快速测试" 或 "3. 自定义配置"
```

### 2. 大规模测试

如果要测试很多配置，使用完整脚本：

1. 编辑 `auto_test_config.json` 设置你想要的配置
2. 运行 `python auto_test_trigger_shapes.py`

### 3. 节省时间的技巧

- **跳过训练**: 如果你已经有训练好的模型，设置 `"skip_train": true`
- **分批测试**: 先测试小的配置，确认没问题后再测试大的配置
- **调整超时**: 根据你的硬件性能调整 `timeout` 参数

### 4. 推荐的测试序列

```json
{
  "trigger_shapes": [
    [1, 1],    // 最小配置，快速验证
    [2, 1],    // 当前配置
    [3, 1],    // 稍大配置
    [2, 2],    // 增加文件数
    [3, 2],    // 平衡配置
    [4, 2],    // 较大配置
    [4, 4]     // 大配置（如果前面效果好）
  ]
}
```

## 故障排除

### 常见问题

1. **找不到 trigger_shape 参数**
   - 检查 `poison_main.py` 中是否有 `self.trigger_shape = (2,1)` 这样的行
   - 确保格式正确

2. **训练或评估失败**
   - 检查日志文件了解具体错误
   - 可能是内存不足或其他环境问题

3. **超时问题**
   - 增加 `timeout` 值
   - 或者先测试小的配置

4. **权限问题**
   - 确保有写入权限
   - 在Windows上可能需要管理员权限

### 手动恢复

如果脚本意外中断，可以手动恢复：

```bash
copy poison_main.py.backup poison_main.py
```

## 扩展功能

### 自定义性能指标提取

如果你的 `eval.py` 输出格式不同，可以修改 `extract_performance_metrics` 函数中的正则表达式。

### 添加新的测试步骤

可以在 `test_single_trigger_shape` 函数中添加其他脚本的执行。

### 并行测试

目前是串行测试，如果需要并行可以修改脚本使用多进程。

## 注意事项

1. **备份重要数据**: 测试前请备份重要的模型和数据
2. **监控资源**: 大的触发器配置可能消耗大量内存和时间
3. **结果分析**: 不仅要看性能指标，还要考虑训练时间和资源消耗
4. **多次测试**: 建议对最佳配置进行多次测试确认稳定性

## 联系支持

如果遇到问题或需要定制功能，请提供：
- 错误日志
- 你的配置文件
- 运行环境信息
