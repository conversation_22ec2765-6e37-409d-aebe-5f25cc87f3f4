import torch
from tqdm import tqdm
import os
import dgl
import pickle as pkl
import matplotlib.pyplot as plt
from scipy.spatial import distance_matrix
from collections import Counter

from darpatc import *
from utils.config import build_args
from model.autoencoder import build_model
from utils.loaddata import load_batch_level_dataset, load_entity_level_dataset, load_metadata
from plot_utils import *

def canonical_sort_rows(adj):
    """
    adj: Tensor[k, T]  (0/1)
    返回: Tensor[k, T] 已按行规范排序
    """
    if adj.dim() != 2:
        raise ValueError('adj must be 2-D')
    k, T = adj.shape
    # 把每行编码成一个可比较的整数键；T ≤ 64 时可用 bit, 否则转字符串
    weights = 2 ** torch.arange(T)          # [0,1,2,4,...] on CPU
    keys = (adj * weights).sum(dim=1)       # shape: [k]
    # argsort 得到升序行索引；若存在全零行，keys 都是 0，排序仍稳定
    perm = torch.argsort(keys)
    return adj[perm]                        # 排序完成

def cal_cosine_similarity(adj_tensor_list_train, adj_tensor_list_test):
    train_feat = adj_tensor_list_train.reshape(-1, adj_tensor_list_train.shape[1] * adj_tensor_list_train.shape[2])
    test_feat = adj_tensor_list_test.reshape(-1, adj_tensor_list_test.shape[1] * adj_tensor_list_test.shape[2])

    # 推荐做归一化
    train_feat = F.normalize(train_feat, p=2, dim=1)
    test_feat = F.normalize(test_feat, p=2, dim=1)

    cos_sim = torch.matmul(test_feat, train_feat.T)  # [N_test, N_train]
    return cos_sim
  
# def find_similar_groups(cos_sim_test_):
#     similar_pairs = torch.where(cos_sim_test_ == 1)
#     similar_pairs = torch.where(torch.isclose(cos_sim_test_, torch.tensor(1.0), rtol=1e-5))
#     parent = list(range(cos_sim_test_.shape[0]))
    
    
#     def find(u):
#         while parent[u] != u:
#             parent[u] = parent[parent[u]]
#             u = parent[u]
#         return u
    
#     for i, j in zip(*similar_pairs):
#         if i != j:  # 忽略对角线 (i==j)
#             root_i = find(i)
#             root_j = find(j)
#             if root_i != root_j:
#                 parent[root_j] = root_i
    
#     # 分组
#     groups = {}
#     for u in range(cos_sim_test_.shape[0]):
#         root = find(u)
#         if root not in groups:
#             groups[root] = []
#         groups[root].append(u)
    
#     return list(groups.values())


def deep_clone_dgl_graph(g):
    # 1. Clone the graph structure (this is a shallow copy of features)
    g_clone = g.clone()
    
    # 2. Deep copy node features
    for key, value in g.ndata.items():
        # Create a new tensor with the same data but in a new memory location
        g_clone.ndata[key] = value.clone()
        
    # 3. Deep copy edge features
    for key, value in g.edata.items():
        g_clone.edata[key] = value.clone()
        
    return g_clone

def structure_analyse(cfg,train_data_all, test_data, malicious):
    if os.path.exists(f'./data/{cfg.dataset}/cos_sim_all.pkl'):
        cos_sim_all = pkl.load(open(f'./data/{cfg.dataset}/cos_sim_all.pkl', 'rb'))
        return cos_sim_all
    
    cos_sim_all = {}
    k = 10  # 最大邻居数
    T = len(cfg.edge_type_dict)  # 边类型数
    adj_tensor_list_train_all = {}
    adj_tensor_list_test_all = {}
    for node_type, mal_nodes_idx in malicious.items(): # 遍历恶意节点
        cos_sim_all[node_type] = {}  # 分'gorup'和'cos_sim'
        adj_tensor_list_train_all[node_type] = []  # 邻接矩阵tensor
        adj_tensor_list_test_all[node_type] = []
        # train data
        for train_g in train_data_all:
            adj_tensor_list = []
            train_nodes_idx = ((train_g.ndata['type']==cfg.node_type_dict[node_type])).nonzero().squeeze()
            for train_node in train_nodes_idx:  # 全局
                neighbors = train_g.predecessors(train_node)
                # truncation
                if len(neighbors) > k:
                    adj_tensor = torch.ones((k, T))
                else:
                    node_id_map = {nid.item(): i for i, nid in enumerate(neighbors)}  # nid:idx   neighbors:原始idx
                    # 创建邻接张量 k x T
                    adj_tensor = torch.zeros((k, T))
                    for node_neighbor in neighbors:
                        e_id = train_g.edge_ids(node_neighbor, train_node)
                        etype = train_g.edata['type'][e_id].item()
                        i = node_id_map[node_neighbor.item()]
                        adj_tensor[i, etype] = 1
                adj_tensor_list.append(canonical_sort_rows(adj_tensor))
            adj_tensor_list_train_all[node_type].append(torch.stack(adj_tensor_list))
        # test data
        for test_node in mal_nodes_idx:  # 遍历恶意节点
            neighbors = test_data.predecessors(test_node)
            if len(neighbors) > k:
                neighbors = neighbors[:k]
                print('恶意节点超过最大邻居数')
                
            node_id_map = {nid.item(): i for i, nid in enumerate(neighbors)}  # nid:idx
            adj_tensor = torch.zeros((k, T))
            for node_neighbor in neighbors:
                e_id = test_data.edge_ids(node_neighbor, test_node)
                etype = test_data.edata['type'][e_id].item()
                i = node_id_map[node_neighbor.item()]
                adj_tensor[i, etype] = 1
            adj_tensor_list_test_all[node_type].append(canonical_sort_rows(adj_tensor))
        adj_tensor_list_test_all[node_type] = torch.stack(adj_tensor_list_test_all[node_type])
        
        train_lengths = [tensor.shape[0] for tensor in adj_tensor_list_train_all[node_type]]  # 记录原始长度
        adj_tensor_list_train_all[node_type] = torch.cat(adj_tensor_list_train_all[node_type], dim=0)  # 训练图tensor合并在一起
        
        # test内部划分group
        row_tuples = [tuple(row.flatten().tolist()) for row in adj_tensor_list_test_all[node_type]]
        # 字典：key = 行内容，value = 索引列表
        from collections import defaultdict
        row_dict = defaultdict(list)
        for idx, row in enumerate(row_tuples):
            row_dict[row].append(idx)
        similar_groups = [indices for indices in row_dict.values()]  # 索引是局部的，相对于adj_tensor_list_test_all[node_type]的顺序
        
        cos_sim = cal_cosine_similarity(adj_tensor_list_train_all[node_type], adj_tensor_list_test_all[node_type][[group[0] for group in similar_groups]])
        cos_sim_all[node_type]['group'] = [[malicious[node_type][node_group] for node_group in group] for group in similar_groups]
        
        cos_sim_split = []
        start = 0
        for length in train_lengths:
            end = start + length
            cos_sim_split.append(cos_sim[:, start:end])  # 拆分对应原始张量的部分
            start = end
        cos_sim_all[node_type]['cos_sim'] = cos_sim_split
        
    with open(f'./data/{cfg.dataset}/cos_sim_all.pkl', 'wb') as f:
        pkl.dump(cos_sim_all, f)
    return cos_sim_all

def get_poisonable_process(cfg,train_data_all):
    # 进程名称
    if not os.path.exists(f'./data/{cfg.dataset}/process_name_dict.json'):
        if cfg.dataset == 'theia':
            process_name_dict = get_process_name_dict_theia()
        elif cfg.dataset == 'cadets':
            process_name_dict = get_process_name_dict_cadets()
        with open('./data/{}/process_name_dict.json'.format(cfg.dataset), 'w', encoding='utf-8') as f:
            json.dump(process_name_dict, f)
    else:
        with open(f'./data/{cfg.dataset}/process_name_dict.json', 'r') as f:
            process_name_dict = json.load(f)  # uuid 2 name
            
    # 可投毒进程名称
    with open(f'./data/{cfg.dataset}/poisonable_process_name.txt', 'r') as f:  # 提前准备好，人工
        poisonable_process_name = [line.strip() for line in f.readlines()]
        
    poisonable_mask_all = []
    for i, data in enumerate(train_data_all):
        process_idx = (data.ndata['type'] == cfg.node_type_dict['SUBJECT_PROCESS']).nonzero().squeeze().tolist()

        poisonable_idx = []
        node_map = cfg.train_node_map[i] # uuid 2 idx
        node_map = {v: k for k, v in node_map.items()}  # idx 2 uuid
        
        for idx in process_idx:
            try:
                if process_name_dict[node_map[idx]] in poisonable_process_name:  # 可投毒进程
                    poisonable_idx.append(idx)
            except:
                pass

        poisonable_mask_all.append(poisonable_idx)
        
    return poisonable_mask_all

def extract_subgraph(graph, node_id, k_hop=1):
    if isinstance(node_id, list):
        all_node = node_id
    else:
        all_node = [node_id]
    # all_node.extend(socket_id)
    # all_node.extend(file_id)
    # all_node = torch.tensor(all_node, device=graph.device)

    subg, new_ids = dgl.khop_in_subgraph(graph, all_node, k=k_hop)
    return subg, new_ids

def generate_triggers_batch(graph, nodes, socket_msg, file_msg, trigger_generator, k_hop, temperature, hard, test=False, batch_size=32):
    """批量生成触发器，减少GPU-CPU数据传输"""
    triggers = []
    device = graph.device

    # 分批处理节点
    for i in range(0, len(nodes), batch_size):
        batch_nodes = nodes[i:i+batch_size]
        # =========
        subgraph, node_idx = extract_subgraph(graph, batch_nodes,k_hop=k_hop)
        subgraph = subgraph.to(device)
        trigger = trigger_generator(subgraph, node_idx, temperature, hard, test=test)
        # =========
        triggers.append(trigger)

    if triggers:
        return torch.cat(triggers, dim=0)
    else:
        # 返回空的触发器tensor
        return torch.empty(0, *trigger_generator.trigger_shape, device=device)

def add_trigger_to_dataset(train_data_all, test_data_all, trigger_generator, addtrigger, candidates_all, malicious_node, mal_socket_msg, mal_file_msg, temperature, hard, k_hop, edge_weight, test=False, batch_size=32):
    trigger_all = []  # 所有触发器（训练集+测试集），用于计算触发器多样性损失

    # 处理训练数据
    for g_i in range(len(train_data_all)):  # 每一个图
        nodes = candidates_all['SUBJECT_PROCESS'][g_i]
        if len(nodes) > 0:
            triggers = generate_triggers_batch(
                train_data_all[g_i], nodes,
                candidates_all['NetFlowObject'][g_i],
                candidates_all['FILE_OBJECT_BLOCK'][g_i],
                trigger_generator, k_hop, temperature, hard, test, batch_size
            )
            trigger_all.append(triggers)
            train_data_all[g_i] = addtrigger(
                train_data_all[g_i],
                candidates_all['SUBJECT_PROCESS'][g_i],
                candidates_all['NetFlowObject'][g_i],
                candidates_all['FILE_OBJECT_BLOCK'][g_i],
                triggers, edge_weight
            )

    # 处理测试数据
    for g_i in range(len(test_data_all)):
        nodes = malicious_node['SUBJECT_PROCESS']
        if len(nodes) > 0:
            triggers = generate_triggers_batch(
                test_data_all[g_i], nodes,
                mal_socket_msg, mal_file_msg,
                trigger_generator, k_hop, temperature, hard, test, batch_size
            )
            trigger_all.append(triggers)
            test_data_all[g_i] = addtrigger(
                test_data_all[g_i],
                malicious_node['SUBJECT_PROCESS'],
                mal_socket_msg, mal_file_msg,
                triggers, edge_weight
            )

    if trigger_all:
        trigger_all = torch.cat(trigger_all, dim=0)
    else:
        # 返回空的触发器tensor
        device = train_data_all[0].device if train_data_all else torch.device('cuda')
        trigger_all = torch.empty(0, *trigger_generator.trigger_shape, device=device)

    return train_data_all, test_data_all, trigger_all

def trigger_viz(train_data_all, trigger_generator, candidates_all, temperature, hard, k_hop):
    trigger_all = []  # 所有触发器（训练集+测试集），用于计算触发器多样性损失
    for g_i in range(len(train_data_all)):
        nodes = candidates_all['SUBJECT_PROCESS'][g_i]
        if len(nodes) > 0:
            triggers = generate_triggers_batch(
                train_data_all[g_i], nodes,
                candidates_all['NetFlowObject'][g_i],
                candidates_all['FILE_OBJECT_BLOCK'][g_i],
                trigger_generator, k_hop, temperature, hard, test=False, batch_size=32
            )
        trigger_all.append(triggers.detach().cpu())
    trigger_all = torch.cat(trigger_all, dim=0)
    
    trigger_unique, counts = trigger_all.unique(return_counts=True, dim=0)
    # for t in trigger_unique:
    #     fig, axes = plt.subplots(1, 3, figsize=(12, 4))
    #     for i, (tensor, ax) in enumerate(zip(t, axes)):
    #         ax.imshow(tensor, cmap='binary', vmin=0, vmax=1)
    #         ax.set_title(f'Tensor {i+1}\nShape: {tuple(tensor.shape)}')
    #         ax.set_xticks(range(tensor.shape[1]))
    #         ax.set_yticks(range(tensor.shape[0]))
    #     plt.tight_layout()
    #     plt.show()
          
def cal_poison_ratio(cfg, train_data_all, candidates_all):
    for node_type, candidates in candidates_all.items():
        if node_type=='SUBJECT_PROCESS':
            poisoned_idx = candidates
        else:
            poisoned_idx = []
            for candidates_graph in candidates:
                poisoned_idx_g = []
                for _, value in candidates_graph.items():
                    poisoned_idx_g.extend(value)
                poisoned_idx.append(list(set(poisoned_idx_g)))
        ratio = sum(len(sub) for sub in poisoned_idx) / sum(len((train_data.ndata['type']==cfg.node_type_dict[node_type]).nonzero().squeeze()) for train_data in train_data_all)
        print(f'{node_type} 投毒率：{ratio}')

def get_connect_num(g, target_nodes, process_list, most_type=None, return_process=False):
    if isinstance(target_nodes, int):  # 单个训练节点
        count = 0
        process_idx = []
        in_neighbors = g.predecessors(target_nodes)
        for node in in_neighbors:
            if node in process_list:  # 可行性过滤
                e_id = g.edge_ids(node, target_nodes)
                etype = g.edata['type'][e_id].item()
                if etype in most_type:
                    process_idx.append(node.item())
                    count += 1
        return count, process_idx if return_process else count
    elif isinstance(target_nodes, list):  # 测试节点group
        count_all = 0  # 恶意节点数目
        etype_all = []  # 边类型
        for target_node in target_nodes:
            count = 0
            etype_list = []
            in_neighbors = g.predecessors(target_node)
            for node in in_neighbors:
                if node in process_list:
                    e_id = g.edge_ids(node, target_node)
                    etype = g.edata['type'][e_id].item()
                    etype_list.append(etype)
                    count += 1
            count_all += count
            etype_all.append(tuple(etype_list))
        counter = Counter(etype_all)
        most_type, _ = counter.most_common(1)[0]
        return round(count_all/len(target_nodes)), most_type
        
def get_exist_file(cfg, choosed_poisoned_nodes, train_data_all, max_file_num):
    if os.path.exists(f'./data/{cfg.dataset}/train_file_msg.json'):
        file_msg = json.load(open(f'./data/{cfg.dataset}/train_file_msg.json', 'r', encoding='utf-8'))
        return file_msg
    file_msg = [{} for _ in range(len(train_data_all))]
    for i in range(len(train_data_all)):
        g = train_data_all[i]
        choosed_nodes = choosed_poisoned_nodes[i]
        count = 0
        for node in choosed_nodes:
            neighbors = torch.unique(torch.cat([g.successors(node), g.predecessors(node)], dim=0))  # 邻居
            file_mask = (g.ndata['type'][neighbors]==cfg.node_type_dict['FILE_OBJECT_BLOCK']).nonzero().squeeze()
            mask = file_mask.tolist() if file_mask.numel()>0 else []
            try:
                sample_num = min(len(mask), max_file_num)
            except:
                mask = [mask]
                sample_num = min(len(mask), max_file_num)
            if sample_num>0:
                sampled_indices = random.sample(mask, sample_num)
                file_node = neighbors[sampled_indices].tolist()
                file_msg[i][node] = file_node
                count += len(file_node)
        print(f'训练集{i} 选取文件数目为{count}/{max_file_num*len(choosed_nodes)}')
        with open(f'./data/{cfg.dataset}/train_file_msg.json', 'w', encoding='utf-8') as f:
            json.dump(file_msg, f)
    return file_msg

def get_process_name_dict_cadets():
    import re

    process_name_dict = {}
    dir = r"F:\DARPA_TC_E3\data\cadets" # json文件所在目录
    for file in os.listdir(dir):  # 节点信息
        if 'json' in file and not '.txt' in file:
            print('reading {} ...'.format(file))
            f = open(os.path.join(dir, file), 'r', encoding='utf-8')
            for line in tqdm(f):
                if not "Event" in line:
                    continue
                subject_uuid = re.findall(
                    '"subject":{"com.bbn.tc.schema.avro.cdm18.UUID":"(.*?)"},(.*?)"exec":"(.*?)",', line)
                try:
                    uuid = subject_uuid[0][0]
                    cmd = subject_uuid[0][-1]
                    process_name_dict[uuid] = cmd
                except:
                    try:
                        process_name_dict[subject_uuid[0][0]] = ["null"]
                    except:
                        pass
    return process_name_dict

def get_process_name_dict_theia():
    import re
    
    pattern_uuid = re.compile(r'uuid\":\"(.*?)\"')
    pattern_type = re.compile(r'type\":\"(.*?)\"')

    process_name_dict = {}
    dir = r"F:\DARPA_TC_E3\data\theia" # json文件所在目录
    for file in os.listdir(dir):  # 节点信息
        if 'json' in file and not '.txt' in file:
            print('reading {} ...'.format(file))
            f = open(os.path.join(dir, file), 'r', encoding='utf-8')
            for line in tqdm(f):
                if 'com.bbn.tc.schema.avro.cdm18.Event' in line or 'com.bbn.tc.schema.avro.cdm18.Host' in line: continue
                if 'com.bbn.tc.schema.avro.cdm18.TimeMarker' in line or 'com.bbn.tc.schema.avro.cdm18.StartMarker' in line: continue
                if 'com.bbn.tc.schema.avro.cdm18.UnitDependency' in line or 'com.bbn.tc.schema.avro.cdm18.EndMarker' in line: continue
                if len(pattern_uuid.findall(line)) == 0: print(line)
                uuid = pattern_uuid.findall(line)[0]
                subject_type = pattern_type.findall(line)

                if len(subject_type) < 1:
                    if 'com.bbn.tc.schema.avro.cdm18.MemoryObject' in line:
                        subject_type = 'MemoryObject'
                    if 'com.bbn.tc.schema.avro.cdm18.NetFlowObject' in line:
                        subject_type = 'NetFlowObject'
                    if 'com.bbn.tc.schema.avro.cdm18.UnnamedPipeObject' in line:
                        subject_type = 'UnnamedPipeObject'
                else:
                    subject_type = subject_type[0]

                if uuid == '00000000-0000-0000-0000-000000000000' or subject_type!='SUBJECT_PROCESS':
                    continue
                
                subject_uuid_path = re.findall('avro.cdm18.Subject":{"uuid":"(.*?)",(.*?)"path":"(.*?)"', line)
           
                try:
                    node_uuid = subject_uuid_path[0][0]
                    node_path = subject_uuid_path[0][2]
                except:
                    node_uuid = uuid
                    node_path = "null"
                process_name_dict[node_uuid] = node_path
    return process_name_dict

def cal_loss_d(out, y):
    return F.nll_loss(out, y)

def cal_loss_g(traindata_poidoned_node_hidden_feature, testdata_mal_node_hidden_feature, k=10, batch_size=1024):
    """
    优化的GPU损失计算函数，支持大tensor的批处理
    """
    device = traindata_poidoned_node_hidden_feature.device
    num_test = testdata_mal_node_hidden_feature.size(0)
    num_train = traindata_poidoned_node_hidden_feature.size(0)

    # 如果数据量不大，直接计算
    if num_test * num_train < 50000000:  # 避免内存溢出
        distances = torch.cdist(testdata_mal_node_hidden_feature, traindata_poidoned_node_hidden_feature, p=2)
        topk_distances, _ = torch.topk(distances, k=k, dim=1, largest=False)
        node_losses = torch.mean(topk_distances, dim=1)
        loss = torch.mean(node_losses)
        return loss

    # 对于大tensor，使用批处理
    all_node_losses = []
    for i in range(0, num_test, batch_size):
        end_i = min(i + batch_size, num_test)
        test_batch = testdata_mal_node_hidden_feature[i:end_i]

        # 计算当前批次与所有训练数据的距离
        distances = torch.cdist(test_batch, traindata_poidoned_node_hidden_feature, p=2)
        topk_distances, _ = torch.topk(distances, k=k, dim=1, largest=False)
        node_losses = torch.mean(topk_distances, dim=1)
        all_node_losses.append(node_losses)

    # 合并所有批次的损失
    all_node_losses = torch.cat(all_node_losses, dim=0)
    loss = torch.mean(all_node_losses)

    return loss

def cal_loss_l2dist(traindata_poidoned_node_hidden_feature,
                           testdata_mal_node_hidden_feature,
                           k=10,
                           margin=0,
                           batch_size=1024):
    """
    使用Hinge Loss计算带边距的攻击损失，支持批处理
    Args:
        margin (float): 距离阈值. 攻击目标是让距离小于这个值.
        batch_size (int): 批处理大小
    """
    num_test = testdata_mal_node_hidden_feature.size(0)
    num_train = traindata_poidoned_node_hidden_feature.size(0)

    # 如果数据量不大，直接计算
    if num_test * num_train < 50000000:
        distances = torch.cdist(testdata_mal_node_hidden_feature, traindata_poidoned_node_hidden_feature, p=2)
        topk_distances, _ = torch.topk(distances, k=k, dim=1, largest=False)
        node_avg_distances = torch.mean(topk_distances, dim=1)
        node_losses = F.relu(node_avg_distances - margin)
        loss = torch.mean(node_losses)
        return loss

    # 对于大tensor，使用批处理
    all_node_losses = []
    for i in range(0, num_test, batch_size):
        end_i = min(i + batch_size, num_test)
        test_batch = testdata_mal_node_hidden_feature[i:end_i]

        distances = torch.cdist(test_batch, traindata_poidoned_node_hidden_feature, p=2)
        topk_distances, _ = torch.topk(distances, k=k, dim=1, largest=False)
        node_avg_distances = torch.mean(topk_distances, dim=1)
        node_losses = F.relu(node_avg_distances - margin)
        all_node_losses.append(node_losses)

    all_node_losses = torch.cat(all_node_losses, dim=0)
    loss = torch.mean(all_node_losses)

    return loss

def cal_loss_sep(traindata_poidoned_node_hidden_feature,
                           testdata_mal_node_hidden_feature,
                           k=10,
                           margin=0,
                           batch_size=1024):
    """分离损失计算，支持批处理"""
    num_test = testdata_mal_node_hidden_feature.size(0)
    num_train = traindata_poidoned_node_hidden_feature.size(0)

    # 如果数据量不大，直接计算
    if num_test * num_train < 50000000:
        distances = torch.cdist(testdata_mal_node_hidden_feature, traindata_poidoned_node_hidden_feature, p=2)
        topk_distances, _ = torch.topk(distances, k=k, dim=1, largest=False)
        node_avg_distances = torch.mean(topk_distances, dim=1)
        node_losses = F.relu(margin - node_avg_distances)
        loss = torch.mean(node_losses)
        return loss

    # 对于大tensor，使用批处理
    all_node_losses = []
    for i in range(0, num_test, batch_size):
        end_i = min(i + batch_size, num_test)
        test_batch = testdata_mal_node_hidden_feature[i:end_i]

        distances = torch.cdist(test_batch, traindata_poidoned_node_hidden_feature, p=2)
        topk_distances, _ = torch.topk(distances, k=k, dim=1, largest=False)
        node_avg_distances = torch.mean(topk_distances, dim=1)
        node_losses = F.relu(margin - node_avg_distances)
        all_node_losses.append(node_losses)

    all_node_losses = torch.cat(all_node_losses, dim=0)
    loss = torch.mean(all_node_losses)
    return loss

# def cal_divs_loss(trigger_all, GW, device):
#     '''软化'''
#     trigger_all = trigger_all.view(trigger_all.shape[0], -1)  # [N, D]
#     trigger_bin = GW(trigger_all, 0.5, device)  # 硬化
#     l2_dist = ((trigger_bin.unsqueeze(1) - trigger_bin.unsqueeze(0)) ** 2).sum(-1)
#     mask = torch.triu(torch.ones_like(l2_dist), diagonal=1).bool()
#     mean_l2 = l2_dist[mask].mean()
#     diversity_loss = -mean_l2 / trigger_all.shape[1]
#     return diversity_loss

def cal_divs_loss(trigger_all):
    '''硬化'''
    trigger_all = trigger_all.view(trigger_all.shape[0], -1)  # [N, D]
    l2_dist = ((trigger_all.unsqueeze(1) - trigger_all.unsqueeze(0)) ** 2).sum(-1)
    mask = torch.triu(torch.ones_like(l2_dist), diagonal=1).bool()
    mean_l2 = l2_dist[mask].mean()
    diversity_loss = -mean_l2 / trigger_all.shape[1]
    return diversity_loss

def globalize(local_idx_all, data_num_all):
    global_idx = []
    count = 0
    for i, local_idx in enumerate(local_idx_all):
        local_idx = [idx+count for idx in local_idx]
        global_idx.extend(local_idx)
        count += data_num_all[i]
    return global_idx  
    



    